#!/usr/bin/env python3
"""
Script to extract names from HTML file between specified start and end files.
Extracts all text content from elements with data-id="heroField" attribute.
"""

import re
import csv
from pathlib import Path

def extract_names_from_html(html_file_path, start_name, end_name, output_csv_path):
    """
    Extract names from HTML file between start and end markers.
    
    Args:
        html_file_path: Path to the HTML file
        start_name: Starting file name to begin extraction
        end_name: Ending file name to stop extraction
        output_csv_path: Path for the output CSV file
    """
    
    # Read the HTML file
    with open(html_file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Find the start position
    start_pattern = re.escape(start_name)
    start_match = re.search(rf'data-id="heroField"[^>]*>{start_pattern}</span>', content)
    
    if not start_match:
        print(f"Start file '{start_name}' not found!")
        return
    
    start_pos = start_match.start()
    print(f"Found start file at position: {start_pos}")
    
    # Find the end position
    end_pattern = re.escape(end_name)
    end_match = re.search(rf'data-id="heroField"[^>]*>{end_pattern}</span>', content)
    
    if not end_match:
        print(f"End file '{end_name}' not found!")
        return
    
    end_pos = end_match.end()
    print(f"Found end file at position: {end_pos}")
    
    # Extract the content between start and end positions
    target_content = content[start_pos:end_pos]
    
    # Find all heroField elements in the target content
    hero_pattern = r'data-id="heroField"[^>]*>([^<]+)</span>'
    matches = re.findall(hero_pattern, target_content)
    
    print(f"Found {len(matches)} names between '{start_name}' and '{end_name}'")
    
    # Write to CSV file
    with open(output_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # Write header
        writer.writerow(['Nombre'])
        
        # Write all the extracted names
        for name in matches:
            # Clean up any HTML entities or extra whitespace
            clean_name = name.strip()
            writer.writerow([clean_name])
    
    print(f"Successfully extracted {len(matches)} names to '{output_csv_path}'")
    
    # Print first few names as preview
    print("\nFirst 10 names extracted:")
    for i, name in enumerate(matches[:10]):
        print(f"{i+1:3d}. {name}")
    
    if len(matches) > 10:
        print(f"... and {len(matches) - 10} more names")

if __name__ == "__main__":
    # Configuration
    html_file = "Untitled-3.html"
    start_file = "NO LIMITS 30seg fr V22_URL FR.mp4"
    end_file = "ZONA 5 ESPERANZA.mov"
    output_file = "nombres_extraidos.csv"
    
    # Check if HTML file exists
    if not Path(html_file).exists():
        print(f"Error: HTML file '{html_file}' not found!")
        exit(1)
    
    # Extract names
    extract_names_from_html(html_file, start_file, end_file, output_file)
