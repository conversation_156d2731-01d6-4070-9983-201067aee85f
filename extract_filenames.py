#!/usr/bin/env python3
"""
Script to extract filenames from HTML table and save to CSV
"""

import re
import csv
from html import unescape

def extract_filenames_from_html(html_file_path):
    """Extract all filenames from HTML file"""

    with open(html_file_path, 'r', encoding='utf-8') as file:
        content = file.read()

    # Pattern to find all filenames with data-id="heroField"
    pattern = r'data-id="heroField"[^>]*>([^<]+)</span>'
    matches = re.findall(pattern, content)

    # Clean up filenames (decode HTML entities)
    filenames = []
    for match in matches:
        filename = unescape(match.strip())
        if filename:
            filenames.append(filename)

    print(f"Found {len(filenames)} total filenames")

    return filenames

def save_to_csv(filenames, output_file):
    """Save filenames to CSV file"""
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['Filename'])  # Header
        
        for filename in filenames:
            writer.writerow([filename])
    
    print(f"Saved {len(filenames)} filenames to {output_file}")

def filter_filenames_by_range(filenames, start_pattern, end_filename):
    """Filter filenames between start pattern and end filename"""

    # Find starting index - look for files containing "NO LIMITS" and "FR"
    start_index = None
    end_index = None

    # Look for files that might match the start pattern
    for i, filename in enumerate(filenames):
        if "NO LIMITS" in filename and "FR" in filename:
            start_index = i
            print(f"Found starting file: {filename} at index {i}")
            break

    # If no FR version found, look for any NO LIMITS file
    if start_index is None:
        for i, filename in enumerate(filenames):
            if "NO LIMITS" in filename:
                start_index = i
                print(f"Found alternative starting file: {filename} at index {i}")
                break

    # Find ending index
    for i, filename in enumerate(filenames):
        if filename == end_filename:
            end_index = i
            print(f"Found ending file: {filename} at index {i}")
            break

    if start_index is None:
        print("No suitable starting file found")
        return filenames  # Return all if no start found

    if end_index is None:
        print("Ending file not found")
        return filenames  # Return all if no end found

    # Extract range (inclusive)
    filtered = filenames[start_index:end_index + 1]
    print(f"Filtered to {len(filtered)} filenames (from index {start_index} to {end_index})")

    return filtered

def main():
    html_file = "Untitled-3.html"
    output_file_all = "all_filenames.csv"
    output_file_filtered = "filenames_filtered.csv"

    print("Extracting filenames from HTML...")
    filenames = extract_filenames_from_html(html_file)

    if filenames:
        print("\nFirst 5 filenames:")
        for i, filename in enumerate(filenames[:5]):
            print(f"{i+1}: {filename}")

        print(f"\nLast 5 filenames:")
        for i, filename in enumerate(filenames[-5:], len(filenames)-4):
            print(f"{i}: {filename}")

        # Save all filenames
        save_to_csv(filenames, output_file_all)
        print(f"\nAll filenames saved to: {output_file_all}")

        # Filter based on user's range request
        print("\n" + "="*50)
        print("Filtering based on your range request...")
        filtered_filenames = filter_filenames_by_range(
            filenames,
            "NO LIMITS 30seg fr V22_URL FR.mp4",  # Start pattern
            "ZONA 5 ESPERANZA.mov"  # End filename
        )

        if filtered_filenames:
            print(f"\nFiltered list ({len(filtered_filenames)} files):")
            for i, filename in enumerate(filtered_filenames, 1):
                print(f"{i}: {filename}")

            save_to_csv(filtered_filenames, output_file_filtered)
            print(f"\nFiltered filenames saved to: {output_file_filtered}")

    else:
        print("No filenames extracted")

if __name__ == "__main__":
    main()
