#!/usr/bin/env python3
"""
Script to extract filenames from HTML table and save to CSV
"""

import re
import csv

def extract_filenames_from_html(html_file_path):
    """Extract filenames from HTML file between specified start and end files"""
    
    with open(html_file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Pattern to find all filenames with data-id="heroField"
    pattern = r'data-id="heroField" data-is-focusable="true">([^<]+)</span>'
    matches = re.findall(pattern, content)
    
    # Find the starting and ending indices
    start_filename = "KITESURF - NO LIMITS - ALEMÁN V22_URL DE.mp4"  # Using this as starting point
    end_filename = "ZONA 5 ESPERANZA.mov"
    
    start_index = None
    end_index = None
    
    # Look for alternative starting filenames if the exact one is not found
    alternative_starts = [
        "KITESURF - NO LIMITS - ESPAÑOL V22.mp4",
        "KITESURF - NO LIMITS - ALEMÁN V22_URL DE.mp4"
    ]
    
    for i, filename in enumerate(matches):
        # Check for starting filename
        if filename in alternative_starts and start_index is None:
            start_index = i
            print(f"Found starting file: {filename} at index {i}")
        
        # Check for ending filename
        if filename == end_filename:
            end_index = i
            print(f"Found ending file: {filename} at index {i}")
            break
    
    if start_index is None:
        print("Starting filename not found. Available filenames:")
        for i, filename in enumerate(matches[:10]):  # Show first 10
            print(f"{i}: {filename}")
        return []
    
    if end_index is None:
        print("Ending filename not found")
        return []
    
    # Extract filenames between start and end (inclusive)
    selected_filenames = matches[start_index:end_index + 1]
    
    print(f"Extracted {len(selected_filenames)} filenames from index {start_index} to {end_index}")
    
    return selected_filenames

def save_to_csv(filenames, output_file):
    """Save filenames to CSV file"""
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['Filename'])  # Header
        
        for filename in filenames:
            writer.writerow([filename])
    
    print(f"Saved {len(filenames)} filenames to {output_file}")

def main():
    html_file = "Untitled-3.html"
    output_file = "filenames_list.csv"
    
    print("Extracting filenames from HTML...")
    filenames = extract_filenames_from_html(html_file)
    
    if filenames:
        print("\nFirst 5 filenames:")
        for i, filename in enumerate(filenames[:5]):
            print(f"{i+1}: {filename}")
        
        print(f"\nLast 5 filenames:")
        for i, filename in enumerate(filenames[-5:], len(filenames)-4):
            print(f"{i}: {filename}")
        
        save_to_csv(filenames, output_file)
        print(f"\nCSV file created successfully: {output_file}")
    else:
        print("No filenames extracted")

if __name__ == "__main__":
    main()
